<?php

namespace app\controller\api\compatible;

use app\controller\api\Controller;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class OpenaiController extends Controller
{
    #[Post('openai/v1/chat/completions')]
    public function completions()
    {
        $params = $this->validate([
            'model'            => 'require',
            'messages'         => 'require|array',
            'tools'            => 'array',
            'tool_choice'      => '',
            'max_tokens'       => 'integer',
            'temperature'      => 'float',
            'top_p'            => 'float',
            'frequency_penalty' => 'float',
            'presence_penalty' => 'float',
            'stop'             => '',
            'stream'           => 'bool',
            'user'             => '',
            'seed'             => 'integer',
            'response_format'  => '',
            'thinking'         => 'in:enabled,disabled,auto',
            'moderation'       => 'bool',
            'prompt_cache_key' => '',
        ]);

        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            return json($result->getReturn());
        }

        $generator = function () use ($result) {
            foreach ($result as $event) {
                yield 'data: ' . json_encode($event) . "\n\n";
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    public function getTokenFromRequest()
    {
        $authorization = $this->request->header('authorization');
        if ($authorization && str_starts_with($authorization, 'Bearer ')) {
            return substr($authorization, 7);
        }

        return parent::getTokenFromRequest();
    }
}
